# YAPI MCP Server

腾讯云MCP服务器，用于与YAPI系统交互，提供YAPI接口管理和代码生成功能。

## 功能特性
- 添加/更新接口Go语言接口到YAPI
- 新增/更新接口的ts类型定义

## 使用指南
#### 1. 确保已安装Node.js (>=16.x)环境
#### 2. 配置MCPServer
```bash
{
  "mcpServers": {
     "YapiServer": {
      "command": "npx",
      "args": ["-y", "@tencent/mcp-yapi"],
      "env": {
        "config_file": "/data/config/yapi-mcp.json"
      },
      "transportType": "stdio"
    },
  }
}
```
config_file配置可以是任意本地路径的json文件

#### 3. 配置文件yapi-mcp.json说明
支持多个项目的配置
```bash
{
  "projects": [
    {
      "name": "项目名称",
      "server_path": "服务路径",
      "yapi_host": "YAPI地址",
      "project_id": "YAPI项目ID（int）",
      "token": "YAPI项目token",
      "templateId": "模板ID（int）"
    }
  ]
}
```

### 配置详解
#### name
必填，项目名称，即一级目录名称，一级目录一般包含有项目的配置信息，如Go语言项目有go.mod文件，前端项目有package.json文件

#### server_path
非必填，`后台项目`需要配置，提供给前端调用的服务路径，如trpc.xxx.xxx.xxx

#### yapi_host
必填，yapi服务地址
1. 团队yapi域名yapi.gpts.woa.com：http://************:8081
2. Aix项目后台：http://************

#### project_id
必填，yapi项目id，如：1728

#### token
必填，yapi项目token，用于访问yapi接口
![img.png](images/yapi-token.png)
可通过链接{yapi_host}/api/project/get?id={projectId}&token={token}，测试访问是否成功

#### template_id
非必填，`前端项目`需要配置，生成前端ts代码的模板id，如：2\
安装[智能 API 代码生成助手](https://yapi.spraylee.com/)插件后，可以查看模板id
![img.png](images/yapi-plugin1.png)

### 使用示例

#### 实例1：
@/standalone-site-backend\app\controller\standalonesite\dynamics\district.go\
更新GetDistrictList yapi接口

#### 示例2：
新增"SayHello"yapi接口\
packages/types/ 目录存放类型定义
packages/nikke/src/api/ 目录存放接口调用代码
packages/configs/api 目录存放api常量定义
> 可以将提示词放到`Rules`中

#### 实例3：
从Yapi更新SayHello接口类型

### 其他说明
1. 推荐插件：Cline、CodeBuddy、Augment
2. MCP调用效果很大程度依赖底层模型能力，推荐：gpt-4.1，deepseek-v3，claude系列
