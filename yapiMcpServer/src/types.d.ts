// yapi项目配置
export interface ProjectConfig {
  name: string;  // 项目名称，即文件根目录名称
  server_path: string; // 服务端路径
  yapi_host: string; // yapi服务器地址
  project_id: number; // yapi项目id
  token: string; // yapi项目token
}

export interface ProjectParams {
  projectId: number;
  keyword?: string;
}

export interface GetCatInterfaceParams extends ProjectParams {
  catId: number;
}

export interface GetInterfaceDetailParams extends ProjectParams {
  interfaceId: number;
  templateId: number;
}

export interface CreateCategoryParams extends ProjectParams {
  catName: string;
}

// 创建接口参数
export interface InterfaceParams extends ProjectParams {
  type: "add" | "up",
  interfaceId?: number,
  funcName: string,
  currentPb: string,
  modPath: string,
  title: string,
  path: string,
  catId: number,
  method: string
}


export interface CatItem {
  _id: number;
  id: number;
  name: string;
}


export interface YapiIntefaceItem {
  method: string;
  project_id: number;
  catid: number;
  title: string;
  desc: string;
  tag: string[];
  path: string;
  req_body_type: string;
  res_body_type: string;
  res_body_is_json_schema: boolean;
  req_body_is_json_schema: boolean;
  res_body: string;
  req_body_other: string;
  dataSync: string;
}