import winston from 'winston';
import path from 'path';
import dayjs from 'dayjs';

const { combine, timestamp, printf, colorize, align } = winston.format;

// 定义日志格式
const logFormat = printf(({ level, message, timestamp }) => {
  return `${dayjs(timestamp as string).format('YYYY-MM-DD HH:mm:ss')} [${level}]: ${message}`;
});

// 创建日志目录路径
export const logDir = path.join(__dirname, '../logs');

export const logger = winston.createLogger({
  level: 'info',
  format: combine(
    timestamp(),
    logFormat
  ),
  transports: [
    // 控制台输出
    new winston.transports.Console({
      format: combine(
        colorize({ all: true }),
        align(),
        logFormat
      )
    }),
    // 文件输出
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 7
    }),
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 7
    })
  ]
});

