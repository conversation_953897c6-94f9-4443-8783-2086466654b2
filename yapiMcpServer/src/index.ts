#!/usr/bin/env node
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import fs from 'fs';
import { Yapi<PERSON>pi } from "./yapiApi";
import { logger, logDir } from './logger';
import { getConfig } from './utils';
import {
  ProjectConfig, ProjectParams, GetCatInterfaceParams, GetInterfaceDetailParams, InterfaceParams, CreateCategoryParams,
} from './types'

const server = new McpServer({
  name: "YapiServer",
  version: "1.0.0",
});


// 生成项目介绍
function formatProjectsDesc(projectList: ProjectConfig[]) {
  const baseInfo = `当前有多个项目，每个项目包含以下信息：
name: 项目名称，即一级目录名称，一级目录一般包含有项目的配置信息，如Go语言项目有go.mod文件；
server_path: 提供给前端调用的服务路径，如trpc.xx1.xx2.xx3；
yapi_host: yapi地址，如http://************；
project_id: yapi项目id，如1187；
token：yapi项目token，用于访问yapi接口；
template_id：生成前端ts的模板id；
你需要根据用户所处的文件根目录，判断当前用户操作的根目录或者二级目录是哪个项目。如果文件目录匹配不到项目name,则不需要获取未匹配的项目信息。
后续关于接口的信息都是指定yapi接口，不需要在项目目录中查找接口信息。
`;
  const projectsInfo = projectList.map((project, index) => {
    return `项目${index + 1}: ${JSON.stringify(project)}`;
  }).join('\n');
  return `${baseInfo}\nyapi项目列表如下：\n${projectsInfo}`;
}

class YapiMcpServer {
  private yapiApi: YapiApi;
  private projects: ProjectConfig[];

  constructor(projects: ProjectConfig[]) {
    this.projects = projects;
    this.yapiApi = new YapiApi(projects);
  }

  // 注册工具
  registerTools() {
    const infoDesc = formatProjectsDesc(this.projects);

    const getProjectInfoDesc = `获取项目信息，项目名称和接口分类。注意项目名称不一定和一级目录名称相同，根据项目的project_id进行匹配。
params projectId: yapi项目id；
return: 项目名称和接口分类

YapiServer说明：${infoDesc}
`;
    server.tool("getProjectInfo", getProjectInfoDesc,
      {
        projectId: z.number().describe("yapi项目id")
      },
      async (params: ProjectParams) => {
      const project = await this.yapiApi.getProject(params.projectId);
        const { name, cat } = project;
         const projectInfo = `项目名称: ${name}\n ${cat
          .map((item: { name: string, _id: number }) => `分类名称:${item.name}，分类id:${item._id}`)
          .join("\n")}`;
        return {
          content: [{type: "text", text: projectInfo}]
        };
      }
    );

    const createCategoryDesc = `创建接口分类。
params projectId: yapi项目id；
params catName: 分类名称；
return 分类名称和分类id
`;
    server.tool("createCategory", createCategoryDesc,
      {
        projectId: z.number().describe("yapi项目id"),
        catName: z.string().describe("分类名称")
      },
      async (params: CreateCategoryParams) => {
        const createRes = await this.yapiApi.createCategory(params.projectId, params.catName);
        const { name, id } = createRes;
        return {
          content: [{type: "text", text: `分类名称:${name}，分类id:${id}` }]
        };
      }
    );


    const getAllInterfaceDesc = `获取当前项目所有接口。
params projectId: 项目id；
params keyword: 接口名称关键词，用于模糊搜索接口，可选参数；
return: 格式化后的接口列表信息
`;
    server.tool("getAllInterface", getAllInterfaceDesc,
      {
        projectId: z.number().describe("yapi项目id"),
        keyword: z.string().describe("接口名称关键词，用于模糊搜索接口，可选参数")
      },
      async (params: ProjectParams) => {
        const allInterface = await this.yapiApi.getAllInterface(params.projectId, params.keyword);
        return {
          content: [{type: "text", text: allInterface || ''}]
        };
      }
    );


    const getCatInterfaceDesc = `获取当前项目分类下的所有接口，包含接口id、分类id、名称、创建时间、方法、路径等信息。
params projectId: yapi项目id；
params catId: 分类id；
return: 指定分类下的接口列表信息
`;
    server.tool("getCatInterface", getCatInterfaceDesc,
      {
        projectId: z.number().describe("yapi项目id"),
        catId: z.number().describe("分类id")
      },
      async (params: GetCatInterfaceParams) => {
        const allInterface = await this.yapiApi.getCatInterface(params.projectId, params.catId);
        return {
          content: [{type: "text", text: allInterface || ''}]
        };
      }
    );


    const genInterfaceTsCodeDesc = `根据yapi接口，生成对应的ts类型定义，包括接口信息、请求参数类型定义、响应参数类型定义。
params projectId: yapi项目id；
params templateId: 生成ts代码的模板id；
params interfaceId: yapi接口id，需要先调用getAllInterfaceDesc接口获取接口id；
return: 接口信息、请求参数类型定义、响应参数类型定义
`;
    server.tool("genInterfaceTsCodeDesc", genInterfaceTsCodeDesc,
      {
        projectId: z.number().describe("yapi项目id"),
        templateId: z.number().describe("生成ts代码的模板id"),
        interfaceId: z.number().describe("接口id，需要先调用getAllInterfaceDesc接口获取接口id")
      },
      async (params: GetInterfaceDetailParams) => {
        const interfaceDetail = await this.yapiApi.generateInterfaceTsCode(params);
        return {
          content: [{type: "text", text: interfaceDetail}]
        };
      }
    );


    const addGoInterfaceDesc = `根据Go语言代码的请求参数和返回参数类型定义，添加接口到yapi中。
params type: 添加或更新接口 add or up；
params interfaceId: 接口id，如果type为up，则需要指定接口id；
params funcName: 接口函数名；
params currentPb: 接口函数名所引用的proto文件url，在funcName所在文件中查找，需要完整的域名（eg: git.woa.com/trpcprotocol/xxx/xxx），注意需要在当前文件查找；
params modPath: 当前项目go.mod文件绝对路径（一般在项目一级目录下，且保证路径能够被fs.readFileSync读取）；
params projectId: yapi项目id；
params title: 接口名称，用户未指定则根据注释获取，没有注释则使用funcName；
params path: 接口路径，用户不指定则留空字符串；
params catId: 接口分类id，需要先调用getProjectInfo接口获取所有分类，再找到分类id；
params method: 请求方法，默认为post；
return: 添加结果
`;
    server.tool("addOrUpdateInterface", addGoInterfaceDesc,
      {
        type: z.enum(['add', 'up']).describe("添加或更新接口"),
        interfaceId: z.number().describe("接口id，如果type为up，则需要指定接口id"),
        funcName: z.string().describe("接口函数名"),
        currentPb: z.string().describe("接口函数名所引用的proto文件url，在funcName所在文件中查找，需要完整的域名，（eg: git.woa.com/trpcprotocol/xxx/xxx）"),
        modPath: z.string().describe("当前项目go.mod文件绝对路径（一般在项目一级目录下，且保证路径能够被fs.readFileSync读取）"),
        projectId: z.number().describe("yapi项目id"),
        title: z.string().describe("接口名称，用户未指定则根据注释获取，没有注释则使用funcName"),
        path: z.string().describe("接口路径，用户不指定则留空字符串"),
        catId: z.number().describe("接口分类id，需要先调用getProjectInfo接口获取所有分类，再找到分类id"),
        method: z.string().describe("请求方法，默认为post"),
      },
      async (params: InterfaceParams) => {
        const addRes = await this.yapiApi.addOrUpdateInterface(params);
        return {
          content: [{type: "text", text: addRes}]
        };
      }
    );

    const syncInterfaceDesc = `
使用swagger命令swag init生成swagger json文件，再导入到yapi中
params projectPath 项目一级路径，用于执行swag init命令的文件目录
return 导入结果
`;
    server.tool("syncInterface", syncInterfaceDesc,
      {
        projectPath: z.number().describe("接口id，需要先调用getAllInterfaceDesc接口获取接口id"),
        funcName: z.string().describe("接口函数名"),
      },
      async (params: any) => {
        return {
          content: [{type: "text", text: ''}]
        };
      }
    );
  }
}


async function main() {
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, {recursive: true});
  }
  logger.info('日志目录初始化完成');

  const { valid, projects } = getConfig();

  if (!valid) {
    logger.error('配置文件格式错误，请检查配置文件');
    process.exit();
  }

  const yapiMcpServer = new YapiMcpServer(projects);
  yapiMcpServer.registerTools();

  // 标准输入输出方式启动
  const transport = new StdioServerTransport();
  await server.connect(transport);
  logger.info("Yapi MCP Server running on stdio");
}

main().catch((error) => {
  console.error("Server error:", error);
  process.exit();
});
